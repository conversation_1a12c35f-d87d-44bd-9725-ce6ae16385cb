import { useState, useEffect } from 'react'
import { byteMentorMessages, testimonials } from '../data/constants'

export interface SignupData {
  name: string
  email: string
  age: string
  interests: string[]
}

export interface ChatMessage {
  text: string
  isBot: boolean
}

export function useLandingPageState(onLogin: () => void) {
  // Main state
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const [showSignupModal, setShowSignupModal] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [showFloatingCTA, setShowFloatingCTA] = useState(false)
  
  // Signup state
  const [signupData, setSignupData] = useState<SignupData>({
    name: "",
    email: "",
    age: "",
    interests: [],
  })

  // Chat state
  const [showChatAssistant, setShowChatAssistant] = useState(false)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatInput, setChatInput] = useState("")
  const [emailReminder, setEmailReminder] = useState("")

  // ByteMentor state
  const [byteMentorMessage, setByteMentorMessage] = useState(0)
  const [showByteMentor, setShowByteMentor] = useState(true)

  // Testimonial rotation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  // ByteMentor message rotation
  useEffect(() => {
    const interval = setInterval(() => {
      setByteMentorMessage((prev) => (prev + 1) % byteMentorMessages.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // Scroll progress effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = scrollTop / docHeight
      setScrollProgress(progress)

      // Show floating CTA after 50% scroll
      setShowFloatingCTA(progress > 0.5 && progress < 0.9)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Handlers
  const handleSignup = () => {
    console.log("Signup data:", signupData)
    setShowSignupModal(false)
    onLogin()
  }

  const toggleInterest = (interest: string) => {
    setSignupData((prev) => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter((i) => i !== interest)
        : [...prev.interests, interest],
    }))
  }

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim()) return

    const userMessage = { text: chatInput, isBot: false }
    setChatMessages(prev => [...prev, userMessage])

    // Simulate AI response
    setTimeout(() => {
      const responses = [
        "Great question! NanoHero offers safe, educational tech experiences for young learners.",
        "You can start with our Tutorial Zone for beginner-friendly coding lessons!",
        "Our Hack Lab teaches ethical cybersecurity in a controlled environment.",
        "The Gamer Zone combines gaming with learning - perfect for tech-curious kids!",
        "Want to create? Our Creator Studio helps you build and share projects safely.",
      ]
      // Use a deterministic approach based on message count to avoid hydration issues
      const responseIndex = chatMessages.length % responses.length
      const botResponse = {
        text: responses[responseIndex],
        isBot: true
      }
      setChatMessages(prev => [...prev, botResponse])
    }, 1000)

    setChatInput("")
  }

  const handleEmailReminder = () => {
    if (emailReminder) {
      console.log("Email reminder set for:", emailReminder)
      setEmailReminder("")
      setChatMessages(prev => [...prev, {
        text: "Perfect! I'll send you a reminder to join NanoHero. Check your email soon! 📧",
        isBot: true
      }])
    }
  }

  return {
    // State
    currentTestimonial,
    setCurrentTestimonial,
    showSignupModal,
    setShowSignupModal,
    scrollProgress,
    showFloatingCTA,
    signupData,
    setSignupData,
    showChatAssistant,
    setShowChatAssistant,
    chatMessages,
    setChatMessages,
    chatInput,
    setChatInput,
    emailReminder,
    setEmailReminder,
    byteMentorMessage,
    showByteMentor,
    setShowByteMentor,
    
    // Handlers
    handleSignup,
    toggleInterest,
    handleChatSubmit,
    handleEmailReminder,
  }
}
