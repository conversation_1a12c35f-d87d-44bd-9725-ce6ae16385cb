"use client"

import React, { useState, useEffect, useRef, Suspense, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Float } from '@react-three/drei'
import * as THREE from 'three'
import { TypeAnimation } from 'react-type-animation'
import { ChevronDown, Target, Sparkles, Zap, Cpu, Dna } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface QuantumArrivalHeroProps {
  onScrollToNext: () => void
}

// Create circular texture for glowing particles
function createParticleTexture(): THREE.Texture {
  const canvas = document.createElement('canvas')
  const size = 64
  canvas.width = size
  canvas.height = size

  const context = canvas.getContext('2d')
  if (!context) throw new Error('Could not get canvas context')

  // Create radial gradient for circular glow effect
  const gradient = context.createRadialGradient(
    size / 2, size / 2, 0,
    size / 2, size / 2, size / 2
  )

  gradient.addColorStop(0, 'rgba(34, 211, 238, 1)') // Cyan center
  gradient.addColorStop(0.3, 'rgba(34, 211, 238, 0.8)')
  gradient.addColorStop(0.6, 'rgba(34, 211, 238, 0.4)')
  gradient.addColorStop(1, 'rgba(34, 211, 238, 0)') // Transparent edge

  context.fillStyle = gradient
  context.fillRect(0, 0, size, size)

  const texture = new THREE.CanvasTexture(canvas)
  texture.needsUpdate = true

  return texture
}

// Particle field component for quantum wave collapse effect
function Particles({ count }: { count: any }) {
  const meshRef = useRef<THREE.Points>(null)
  const positionAttributeRef = useRef<THREE.BufferAttribute>(null)
  const [positions, setPositions] = useState<Float32Array | null>(null)
  const [phases, setPhases] = useState<Float32Array | null>(null)

  // Create circular texture for glowing particles
  const particleTexture = useMemo(() => createParticleTexture(), [])

  // Calculate grid resolution based on count
  const resolution = Math.ceil(Math.sqrt(count))

  // Initialize grid positions and phases
  useEffect(() => {
    const totalParticles = resolution * resolution
    const posArray = new Float32Array(totalParticles * 3)
    const phaseArray = new Float32Array(totalParticles)

    for (let x = 0; x < resolution; x++) {
      for (let z = 0; z < resolution; z++) {
        const i = x * resolution + z
        const xPos = (x - resolution / 2) * 0.8 // Spacing between particles
        const zPos = (z - resolution / 2) * 0.8

        // Validate positions before setting
        const safeXPos = isFinite(xPos) ? xPos : 0
        const safeZPos = isFinite(zPos) ? zPos : 0

        // Set initial positions
        posArray.set([safeXPos, 0, safeZPos], i * 3)

        // Random phase for each particle
        phaseArray[i] = Math.random() * Math.PI * 2
      }
    }

    setPositions(posArray)
    setPhases(phaseArray)
  }, [resolution])

  // Animate wave motion like the example
  useFrame(({ clock }) => {
    if (!positionAttributeRef.current || !positions || !phases) return

    const time = clock.getElapsedTime()
    const pos = positionAttributeRef.current.array as Float32Array
    const totalParticles = resolution * resolution

    for (let i = 0; i < totalParticles; i++) {
      const x = i % resolution
      const z = Math.floor(i / resolution)

      // Get world position
      const worldX = (x - resolution / 2) * 0.8
      const worldZ = (z - resolution / 2) * 0.8

      // Simplified, safe wave calculations
      const t = time || 0
      const px = phases[i] || 0
      const wx = worldX || 0
      const wz = worldZ || 0

      // Simple wave motion - no complex math
      const wave1 = Math.sin(wx * 0.05 + t * 1.2 + px) * 4
      const wave2 = Math.sin(wz * 0.04 + t * 0.8) * 3
      const wave3 = Math.sin((wx + wz) * 0.03 + t * 1.5) * 2.5

      // Simple distance calculation
      const dist = Math.sqrt(wx * wx + wz * wz)
      const safeDist = Math.min(dist, 50)
      const radialWave = Math.sin(safeDist * 0.08 - t * 2.5) * 2

      // Combine waves with simple validation
      const finalY = wave1 + wave2 + wave3 + radialWave
      const safeY = isFinite(finalY) ? Math.max(-12, Math.min(12, finalY)) : 0

      // Update Y position
      pos[i * 3 + 1] = safeY
    }

    positionAttributeRef.current.needsUpdate = true

    // Disable bounding sphere computation entirely and set rotation
    if (meshRef.current) {
      // Disable frustum culling to avoid bounding sphere computation
      meshRef.current.frustumCulled = false
      meshRef.current.rotation.y = time * 0.01
    }
  })

  if (!positions || !phases) return null

  return (
    <points ref={meshRef} frustumCulled={false}>
      <bufferGeometry>
        <bufferAttribute
          ref={positionAttributeRef}
          attach="attributes-position"
          array={positions}
          count={positions.length / 3}
          itemSize={3}
          usage={THREE.DynamicDrawUsage}
          args={[positions, 3]}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#22D3EE"
        size={0.5}
        transparent={true}
        opacity={0.3}
        sizeAttenuation={true}
        map={particleTexture}
        alphaTest={0.05}
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}


// Avatar selector preview component
function AvatarPreview() {
  const meshRef = useRef<THREE.Mesh>(null)
  
  useFrame((state) => {
    if (!meshRef.current) return
    
    const time = state.clock.getElapsedTime()
    meshRef.current.rotation.y = time * 0.5
    meshRef.current.position.y = Math.sin(time * 2) * 0.2
  })
  
  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <mesh ref={meshRef}>
        <sphereGeometry args={[0.5, 32, 32]} />
        <meshStandardMaterial 
          color="#00ffff" 
          emissive="#0088ff" 
          emissiveIntensity={0.8}
          transparent
          opacity={0.8}
        />
      </mesh>
    </Float>
  )
}

export function QuantumArrivalHero({ onScrollToNext }: QuantumArrivalHeroProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [showCTA, setShowCTA] = useState(false)
  const [_currentStage, setCurrentStage] = useState(0)
  const [showAvatarSelector, setShowAvatarSelector] = useState(false)

  useEffect(() => {
    setIsLoaded(true)

    // Sequence the intro animations
    const timer1 = setTimeout(() => setCurrentStage(1), 2000)
    const timer2 = setTimeout(() => setCurrentStage(2), 4000)
    const timer3 = setTimeout(() => setShowCTA(true), 6000)
    const timer4 = setTimeout(() => setShowAvatarSelector(true), 8000)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
      clearTimeout(timer4)
    }
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-space-gradient pt-16 sm:pt-20">
      {/* Quantum background overlay */}
      <div className="absolute inset-0 consciousness-wave opacity-30" />

      {/* 3D Background Canvas */}
      <div className="absolute inset-0">
      <Canvas
        camera={{ position: [-10, 10, 20], fov: 55 }}
        gl={{ antialias: false, alpha: true, powerPreference: 'low-power' }}
        style={{ background: 'transparent' }}
        dpr={[1, 1.5]}
      >
          <Suspense fallback={null}>
            {/* Lighting */}
            <ambientLight intensity={0.3} />
            <pointLight position={[10, 10, 10]} intensity={1} color="#22d3ee" />
            <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8b5cf6" />

            {/* Stars background */}
            {/* <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade /> */}

            {/* Quantum particle field */}
            <Particles count={10000} />

            {/* Timeline grid formation */}
            {/* {currentStage >= 1 && <TimelineGrid />} */}

            {/* Avatar preview */}
            {showAvatarSelector && <AvatarPreview />}

            <OrbitControls enableZoom={false} enablePan={false} autoRotate autoRotateSpeed={0.5} />
          </Suspense>
        </Canvas>
      </div>

      {/* Content overlay - Mobile First */}
      <div className="relative z-10 w-full px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 50 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="space-y-6 sm:space-y-8 lg:space-y-12"
        >
          {/* Main Title - Mobile First */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.8 }}
            transition={{ duration: 1, delay: 1 }}
            className="relative px-2 sm:px-4"
          >
            {/* Quantum glow effect behind title */}
            <div className="absolute inset-0 blur-2xl sm:blur-3xl opacity-30 sm:opacity-40">
              <h1 className="text-4xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-hero text-neural-cyan">
                NanoHero
              </h1>
            </div>

            <h1 className="relative text-4xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-hero bg-gradient-to-r from-neural-cyan via-quantum-purple via-quantum-gold to-neural-cyan bg-clip-text text-transparent bg-[length:200%_100%] animate-gradient-x neural-glow leading-tight">
              NanoHero
            </h1>

            {/* Subtitle with typing animation */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              className="mt-4 sm:mt-6 px-2 sm:px-4"
            >
              <TypeAnimation
                sequence={[
                  'Welcome, NanoArchitect...',
                  2000,
                  'Activating Learning Protocols...',
                  2000,        
                  'Quantum Learning Protocols Engaged...',
                  2000,
                  'All Systems Online...',
                  2000,
                  'Welcome to the NanoVerse!',
                  2000
                ]}
                wrapper="p"
                speed={50}
                className="text-base sm:text-xl md:text-2xl lg:text-3xl text-neural-cyan font-subtitle neural-glow leading-relaxed"
                repeat={Infinity}
              />
            </motion.div>
          </motion.div>

          {/* Feature Icons - Mobile First Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 1, delay: 3 }}
            className="w-full px-2 sm:px-4 py-4 sm:py-6 lg:py-8"
          >
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
              {[
                { icon: Zap, label: 'Neural Core', shortLabel: 'Neural', color: '#22d3ee', bgClass: 'bg-neural-cyan' },
                { icon: Cpu, label: 'Quantum AI', shortLabel: 'AI', color: '#8b5cf6', bgClass: 'bg-quantum-purple' },
                { icon: Dna, label: 'Evolution', shortLabel: 'Evolution', color: '#fbbf24', bgClass: 'bg-quantum-gold' },
                { icon: Target, label: 'Precision', shortLabel: 'Precision', color: '#22d3ee', bgClass: 'bg-neural-cyan' }
              ].map((item, index) => (
                <motion.div
                  key={item.label}
                  initial={{ opacity: 0, scale: 0, rotateY: 180 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{
                    delay: 3.5 + index * 0.2,
                    duration: 0.8,
                    type: "spring",
                    stiffness: 100
                  }}
                  whileHover={{ scale: 1.02, rotateY: 2 }}
                  whileTap={{ scale: 0.98 }}
                  className="group relative"
                >
                  {/* Quantum glow effect */}
                  {/* <div
                    className="absolute inset-0 rounded-xl sm:rounded-2xl blur-lg sm:blur-xl opacity-15 group-hover:opacity-30 transition-opacity duration-300"
                    style={{ backgroundColor: item.color }}
                  /> */}

                  {/* <Card className="relative quantum-glass rounded-xl sm:rounded-2xl p-3 sm:p-4 lg:p-6 h-full transition-all duration-300 group-hover:border-opacity-60 quantum-border">
                    <div className="flex flex-col items-center space-y-2 sm:space-y-3 lg:space-y-4">
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                        className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 rounded-full flex items-center justify-center border border-2 quantum-pulse"
                        style={{
                          background: `linear-gradient(135deg, ${item.color}30, ${item.color}10)`,
                          borderColor: item.color,
                          boxShadow: `0 0 15px ${item.color}40`
                        }}
                      >
                        <item.icon className="w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8" style={{ color: item.color }} />
                      </motion.div>
                      <h3 className="text-xs sm:text-sm lg:text-lg font-space-grotesk font-bold neural-glow text-center leading-tight" style={{ color: item.color }}>
                        <span className="sm:hidden">{item.shortLabel}</span>
                        <span className="hidden sm:inline">{item.label}</span>
                      </h3>
                    </div>
                  </Card> */}
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Button - Mobile First */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: showCTA ? 1 : 0, y: showCTA ? 0 : 30 }}
            transition={{ duration: 1 }}
            className="px-4 space-y-4 sm:space-y-6"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="relative group"
            >
              {/* Enhanced quantum glow effect */}
              <div className="absolute -inset-1 sm:-inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl sm:rounded-3xl blur-xl sm:blur-2xl opacity-40 sm:opacity-50 group-hover:opacity-60 sm:group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

              {/* Secondary glow layer */}
              <div className="absolute -inset-0.5 sm:-inset-1 bg-gradient-to-r from-neural-cyan/60 via-quantum-purple/60 to-quantum-gold/60 rounded-xl sm:rounded-2xl blur-md sm:blur-lg opacity-30 sm:opacity-40 group-hover:opacity-50 sm:group-hover:opacity-70 transition-opacity duration-300" />
{/* 
              <Button
                onClick={onScrollToNext}
                className="relative w-full text-sm sm:text-base lg:text-xl font-button px-6 sm:px-8 lg:px-12 py-4 sm:py-5 lg:py-6 quantum-glass border-2 border-neural-cyan/50 hover:border-neural-cyan text-white group-hover:text-neural-cyan transition-all duration-300 neural-glow"
                style={{
                  background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
                  boxShadow: '0 0 20px rgba(34, 211, 238, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}
              >
                <div className="flex items-center justify-center gap-2 sm:gap-3">
                  <motion.div
                    animate={{ rotate: showCTA ? [0, 360] : 0 }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                    className="text-neural-cyan flex-shrink-0"
                  >
                    <Target className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                  </motion.div>

                  <span className="text-center leading-tight">
                    <span className="sm:hidden">ENTER NANOVERSE</span>
                    <span className="hidden sm:inline lg:hidden">COLLAPSE INTO NANOVERSE</span>
                    <span className="hidden lg:inline">COLLAPSE INTO THE NANOVERSE</span>
                  </span>

                  <motion.div
                    animate={{
                      scale: showCTA ? [1, 1.2, 1] : 1,
                      rotate: showCTA ? [0, 180, 360] : 0
                    }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    className="text-quantum-gold flex-shrink-0"
                  >
                    <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                  </motion.div>
                </div>
              </Button> */}
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator - Mobile Responsive */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 5 }}
        className="absolute bottom-4 sm:bottom-6 lg:bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 8, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="flex flex-col items-center space-y-2 sm:space-y-3 cursor-pointer group"
          onClick={onScrollToNext}
        >
          {/* Quantum glow effect for text */}
          <div className="relative px-2">
            <span className="absolute inset-0 text-neural-cyan text-xs sm:text-sm font-space-grotesk font-medium blur-sm opacity-50">
              <span className="sm:hidden">Explore</span>
              <span className="hidden sm:inline">Explore the NanoVerse</span>
            </span>
            <span className="relative text-neural-cyan text-xs sm:text-sm font-space-grotesk font-medium neural-glow group-hover:text-quantum-purple transition-colors duration-300">
              <span className="sm:hidden">Explore</span>
              <span className="hidden sm:inline">Explore the NanoVerse</span>
            </span>
          </div>

          {/* Enhanced chevron with quantum styling */}
          <motion.div
            animate={{
              boxShadow: [
                '0 0 8px rgba(34, 211, 238, 0.5)',
                '0 0 16px rgba(34, 211, 238, 0.8)',
                '0 0 8px rgba(34, 211, 238, 0.5)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border border-2 border-neural-cyan/50 flex items-center justify-center group-hover:border-quantum-purple/70 transition-all duration-300"
            style={{
              background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.1), rgba(139, 92, 246, 0.1))'
            }}
          >
            <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5 text-neural-cyan group-hover:text-quantum-purple transition-colors duration-300" />
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  )
}

