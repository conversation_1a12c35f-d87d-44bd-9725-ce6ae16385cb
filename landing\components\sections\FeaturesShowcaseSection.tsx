"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronRight, Gamepad2, Wand2, Code, Shield, Sparkles, Zap } from 'lucide-react'
import { features } from '../../data/constants'

interface FeaturesShowcaseSectionProps {
  onSignupClick: () => void
}

export function FeaturesShowcaseSection({ onSignupClick }: FeaturesShowcaseSectionProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum particle positions for enhanced animations
  const quantumParticles = Array.from({ length: 15 }, (_, i) => ({
    left: (i * 23 + 7) % 100,
    top: (i * 31 + 11) % 100,
    delay: i * 0.2,
    color: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'
  }))

  // Enhanced binary rain for quantum effect
  const binaryPositions = Array.from({ length: 25 }, (_, i) => ({
    left: (i * 37 + 13) % 100,
    top: (i * 23 + 7) % 100,
    char: i % 2 === 0 ? "1" : "0",
    delay: i * 0.1
  }))

  // Enhanced quantum zone content with neural themes
  const getQuantumZoneContent = (title: string, description: string) => {
    switch (title) {
      case "Tutorial Zone":
        return {
          animation: isClient ? (
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              {/* Neural learning particles */}
              {quantumParticles.slice(0, 8).map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 rounded-full"
                  style={{
                    left: `${particle.left}%`,
                    top: `${particle.top}%`,
                    backgroundColor: '#22d3ee'
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    opacity: [0, 0.8, 0],
                    y: [0, -20, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: particle.delay,
                    ease: "easeInOut"
                  }}
                />
              ))}
              {/* Neural connection lines */}
              <div className="absolute inset-0 opacity-20">
                <svg className="w-full h-full">
                  <motion.path
                    d="M20,20 Q50,10 80,20 T140,20"
                    stroke="#22d3ee"
                    strokeWidth="1"
                    fill="none"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  />
                </svg>
              </div>
            </div>
          ) : null,
          quantumColor: '#22d3ee',
          cta: "Begin Neural Learning",
          preview: "Master quantum coding fundamentals through neural-enhanced tutorials"
        }
      case "Hack Lab":
        return {
          animation: isClient ? (
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              {/* Quantum security matrix */}
              {binaryPositions.slice(0, 15).map((pos, i) => (
                <motion.div
                  key={i}
                  className="absolute text-xs font-mono font-bold"
                  style={{
                    left: `${pos.left}%`,
                    top: `${pos.top}%`,
                    color: '#8b5cf6'
                  }}
                  animate={{
                    y: [0, 100],
                    opacity: [0, 1, 0],
                    scale: [0.8, 1, 0.8]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: pos.delay,
                  }}
                >
                  {pos.char}
                </motion.div>
              ))}
              {/* Quantum security shield */}
              <motion.div
                className="absolute top-4 right-4 w-8 h-8 border-2 border-quantum-purple rounded-full"
                animate={{
                  rotate: 360,
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  rotate: { duration: 4, repeat: Number.POSITIVE_INFINITY, ease: "linear" },
                  scale: { duration: 2, repeat: Number.POSITIVE_INFINITY }
                }}
              >
                <div className="absolute inset-1 bg-quantum-purple/20 rounded-full" />
              </motion.div>
            </div>
          ) : null,
          quantumColor: '#8b5cf6',
          cta: "Enter Quantum Lab",
          preview: "Explore quantum cryptography and consciousness security protocols"
        }
      case "Gamer Zone":
        return {
          animation: isClient ? (
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              {/* Gaming quantum field */}
              {quantumParticles.slice(0, 10).map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2"
                  style={{
                    left: `${particle.left}%`,
                    top: `${particle.top}%`,
                  }}
                  animate={{
                    rotate: 360,
                    scale: [0.5, 1.2, 0.5],
                    opacity: [0.3, 0.8, 0.3]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: particle.delay
                  }}
                >
                  <div className="w-full h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full" />
                </motion.div>
              ))}
              {/* Quantum gamepad */}
              <motion.div
                className="absolute top-3 right-3"
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 4,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeInOut"
                }}
              >
                <Gamepad2 className="w-7 h-7 text-purple-400" />
              </motion.div>
            </div>
          ) : null,
          quantumColor: '#a855f7',
          cta: "Join Quantum Games",
          preview: "Challenge consciousness through quantum gaming experiences"
        }
      case "Creator Studio":
        return {
          animation: isClient ? (
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              {/* Creative quantum sparks */}
              {quantumParticles.slice(0, 12).map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute"
                  style={{
                    left: `${particle.left}%`,
                    top: `${particle.top}%`,
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    rotate: [0, 180, 360],
                    opacity: [0, 1, 0]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: particle.delay
                  }}
                >
                  <Sparkles className="w-3 h-3 text-quantum-gold" />
                </motion.div>
              ))}
              {/* Quantum creation wand */}
              <motion.div
                className="absolute top-3 right-3"
                animate={{
                  rotate: [0, 15, -15, 0],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Number.POSITIVE_INFINITY
                }}
              >
                <Wand2 className="w-7 h-7 text-quantum-gold" />
              </motion.div>
            </div>
          ) : null,
          quantumColor: '#fbbf24',
          cta: "Start Creating",
          preview: "Build quantum realities and consciousness-driven experiences"
        }
      default:
        return {
          animation: isClient ? (
            <div className="absolute inset-0 overflow-hidden rounded-xl opacity-30">
              {quantumParticles.slice(0, 6).map((particle, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                  style={{
                    left: `${particle.left}%`,
                    top: `${particle.top}%`,
                  }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: particle.delay
                  }}
                />
              ))}
            </div>
          ) : null,
          quantumColor: '#22d3ee',
          cta: "Explore Quantum Zone",
          preview: description
        }
    }
  }

  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum background field */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* Quantum field particles */}
        {isClient && quantumParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.6, 0],
              scale: [0.5, 1, 0.5],
              y: [0, -10, 0]
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-quantum-gold/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          {/* Quantum title with enhanced styling */}
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Quantum Learning
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              Universe
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Six specialized <span className="text-neural-cyan font-semibold">quantum domains</span> designed to nurture
            consciousness, creativity, and technical mastery through
            <span className="text-quantum-purple font-semibold"> neural-enhanced learning</span> experiences
          </motion.p>

          {/* Quantum subtitle decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-neural-cyan" />
            <Zap className="w-5 h-5 text-quantum-gold" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        {/* Quantum Learning Zones Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const quantumContent = getQuantumZoneContent(feature.title, feature.description)

            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.15 }}
                whileHover={{ scale: 1.03, y: -8 }}
                className="group"
              >
                <Card
                  className="quantum-glass border-2 h-full relative overflow-hidden group-hover:border-opacity-60 transition-all duration-500"
                  style={{
                    borderColor: `${quantumContent.quantumColor}40`,
                    background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                    boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${quantumContent.quantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                  }}
                >
                  {/* Quantum glow effect on hover */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"
                    style={{
                      background: `linear-gradient(135deg, ${quantumContent.quantumColor}30, ${quantumContent.quantumColor}10)`,
                      boxShadow: `0 0 40px ${quantumContent.quantumColor}40`
                    }}
                  />

                  {/* Quantum sweep effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                    <div
                      className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-xl"
                      style={{
                        background: `linear-gradient(90deg, transparent, ${quantumContent.quantumColor}30, transparent)`
                      }}
                    />
                  </div>

                  {/* Zone-specific quantum animations */}
                  {quantumContent.animation}

                  <CardContent className="p-6 lg:p-8 relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <motion.div
                        className="relative p-4 rounded-xl border-2 group-hover:scale-110 transition-transform duration-300"
                        style={{
                          background: `linear-gradient(135deg, ${quantumContent.quantumColor}20, ${quantumContent.quantumColor}10)`,
                          borderColor: quantumContent.quantumColor,
                          boxShadow: `0 0 20px ${quantumContent.quantumColor}40`
                        }}
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.8 }}
                      >
                        <feature.icon className="w-6 h-6 lg:w-7 lg:h-7 text-white" />

                        {/* Icon quantum glow */}
                        <div
                          className="absolute inset-0 rounded-xl blur-lg opacity-50"
                          style={{ backgroundColor: quantumContent.quantumColor }}
                        />
                      </motion.div>

                      <div className="flex-1">
                        <h3
                          className="text-xl lg:text-2xl font-bold text-white group-hover:text-opacity-90 transition-all duration-300 font-orbitron"
                          style={{
                            textShadow: `0 0 20px ${quantumContent.quantumColor}60`
                          }}
                        >
                          {feature.title}
                        </h3>
                        <Badge
                          className="mt-2 text-xs font-medium"
                          style={{
                            backgroundColor: `${quantumContent.quantumColor}20`,
                            borderColor: `${quantumContent.quantumColor}40`,
                            color: quantumContent.quantumColor
                          }}
                        >
                          {feature.stats}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-white/80 mb-4 leading-relaxed text-sm lg:text-base font-space-grotesk">
                      {quantumContent.preview}
                    </p>
                    <p className="text-white/60 mb-6 leading-relaxed text-xs lg:text-sm">
                      {feature.description}
                    </p>

                    <motion.div
                      className="flex items-center group-hover:translate-x-2 transition-all duration-300 cursor-pointer"
                      style={{ color: quantumContent.quantumColor }}
                      whileHover={{ x: 5 }}
                    >
                      <span className="text-sm lg:text-base font-medium font-space-grotesk">
                        {quantumContent.cta}
                      </span>
                      <ChevronRight className="w-4 h-4 lg:w-5 lg:h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </motion.div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Quantum Call-to-Action Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <motion.div
            className="relative quantum-glass rounded-3xl p-8 lg:p-12 border-2 overflow-hidden"
            style={{
              borderColor: '#22d3ee40',
              background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
              boxShadow: `0 0 40px #22d3ee20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
            }}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {/* Quantum background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-30" />
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-neural-cyan/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-40 h-40 bg-quantum-purple/20 rounded-full blur-3xl" />

            {/* Quantum particles for CTA */}
            {isClient && quantumParticles.slice(0, 8).map((particle, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                style={{
                  left: `${particle.left}%`,
                  top: `${particle.top}%`,
                }}
                animate={{
                  opacity: [0, 0.8, 0],
                  scale: [0.5, 1.2, 0.5],
                  rotate: 360
                }}
                transition={{
                  duration: 3,
                  repeat: Number.POSITIVE_INFINITY,
                  delay: particle.delay
                }}
              />
            ))}

            <div className="relative z-10">
              <motion.h3
                className="text-2xl lg:text-3xl font-bold text-white mb-4 font-orbitron"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
              >
                <span className="text-neural-cyan">⚡</span> Ready to Begin Your{' '}
                <span className="bg-gradient-to-r from-neural-cyan to-quantum-purple bg-clip-text text-transparent">
                  Quantum Journey
                </span>?
              </motion.h3>

              <p className="text-base sm:text-lg lg:text-xl text-white/80 mb-8 lg:mb-10 max-w-3xl mx-auto font-space-grotesk leading-relaxed">
                Every <span className="text-neural-cyan font-semibold">NanoHero</span> starts somewhere.
                Pick your first quantum domain and begin building real consciousness skills through hands-on neural challenges!
              </p>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                {/* Enhanced quantum glow effect */}
                <div className="absolute -inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

                <Button
                  onClick={onSignupClick}
                  variant="quantum"
                  size="xl"
                  className="relative bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold text-white font-bold shadow-2xl hover:shadow-neural-cyan/50 transition-all duration-500 border-2 border-neural-cyan/40 font-orbitron"
                >
                  <Sparkles className="w-6 h-6 mr-3" />
                  Initialize Quantum Learning Protocol
                  <Zap className="w-6 h-6 ml-3" />
                </Button>
              </motion.div>

              {/* Secondary quantum actions */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={onSignupClick}
                    variant="outline"
                    size="lg"
                    className="border-neural-cyan/50 text-neural-cyan hover:bg-neural-cyan/10 px-6 py-3 font-semibold rounded-xl font-space-grotesk"
                  >
                    <Code className="w-5 h-5 mr-2" />
                    Start Neural Learning
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={onSignupClick}
                    variant="outline"
                    size="lg"
                    className="border-quantum-purple/50 text-quantum-purple hover:bg-quantum-purple/10 px-6 py-3 font-semibold rounded-xl font-space-grotesk"
                  >
                    <Shield className="w-5 h-5 mr-2" />
                    Enter Quantum Lab
                  </Button>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
