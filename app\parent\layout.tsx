import type { Metada<PERSON> } from 'next'
import { AuthProvider } from '@/contexts/AuthContext'
import { Toaster } from 'sonner'

export const metadata: Metadata = {
  title: 'Parent Dashboard - NanoHero',
  description: 'Monitor your child\'s learning progress and manage safety settings',
}

export default function ParentLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      {children}
      <Toaster 
        position="top-center"
        toastOptions={{
          style: {
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            border: '1px solid rgba(34, 211, 238, 0.3)',
          },
        }}
      />
    </AuthProvider>
  )
}
