"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'
import { Rocket, MessageCircle } from 'lucide-react'

interface FinalCTASectionProps {
  showSignupModal: boolean
  setShowSignupModal: (show: boolean) => void
}

export function FinalCTASection({ showSignupModal, setShowSignupModal }: FinalCTASectionProps) {
  return (
    <section className="px-6 py-20 bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-purple-500/10">
      <div className="max-w-4xl mx-auto text-center">
        <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }}>
          <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
            Ready to Begin?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
            Join thousands of young learners discovering their potential in a safe, inspiring environment.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
              <DialogTrigger asChild>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white px-12 py-4 text-xl font-semibold rounded-xl shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
                >
                  <Rocket className="w-6 h-6 mr-2" />
                  Start Learning Today
                </Button>
              </DialogTrigger>
            </Dialog>
            <Button
              variant="outline"
              size="lg"
              className="border-gray-700 hover:bg-gray-800 px-12 py-4 text-xl font-semibold rounded-xl bg-transparent"
            >
              <MessageCircle className="w-6 h-6 mr-2" />
              Talk to Our Team
            </Button>
          </div>

          <p className="text-gray-400 text-lg">
            <span className="text-green-400 font-semibold">100% Free to Start</span> •{" "}
            <span className="text-blue-400 font-semibold">No Credit Card Required</span> •{" "}
            <span className="text-purple-400 font-semibold">Cancel Anytime</span>
          </p>
        </motion.div>
      </div>
    </section>
  )
}
