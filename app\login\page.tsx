"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter, useSearchParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import {
  Chrome,
  Facebook,
  Loader2,
  Sparkles,
  Zap,
  Shield,
  Users,
  Rocket,
  ArrowLeft,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

export default function LoginPage() {
  const { signInWithGoogle, signInWithFacebook, user: _user, loading: _loading } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [loadingProvider, setLoadingProvider] = useState<'google' | 'facebook' | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [selectedRole, setSelectedRole] = useState<'student' | 'parent' | 'admin'>('student')
  const _router = useRouter()
  const searchParams = useSearchParams()

  // Role definitions
  const roles = [
    {
      id: 'student' as const,
      name: 'Student',
      description: 'Access the learning platform and join the NanoHero journey',
      icon: Rocket,
      color: 'from-cyan-500 to-blue-500',
      borderColor: 'border-cyan-500/30',
      textColor: 'text-cyan-400'
    },
    {
      id: 'parent' as const,
      name: 'Parent',
      description: 'Monitor your child\'s progress and manage safety settings',
      icon: Shield,
      color: 'from-green-500 to-emerald-500',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400'
    },
    {
      id: 'admin' as const,
      name: 'Admin',
      description: 'Administrative access to platform management',
      icon: Users,
      color: 'from-purple-500 to-pink-500',
      borderColor: 'border-purple-500/30',
      textColor: 'text-purple-400'
    }
  ]

  // Check for error in URL params
  useEffect(() => {
    const errorParam = searchParams.get('error')
    if (errorParam) {
      switch (errorParam) {
        case 'auth_failed':
          setError('Authentication failed. Please try again.')
          break
        case 'unexpected':
          setError('An unexpected error occurred. Please try again.')
          break
        default:
          setError('An error occurred during sign in.')
      }
    }
  }, [searchParams])

  // DEVELOPMENT: Disabled automatic redirect for testing
  // TODO: Re-enable for production
  // // Redirect if already authenticated
  // useEffect(() => {
  //   if (user && !loading) {
  //     router.push('/dashboard')
  //   }
  // }, [user, loading, router])

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    setLoadingProvider('google')
    setError(null)

    try {
      await signInWithGoogle(selectedRole)
      // Note: Supabase OAuth will redirect automatically, so we don't need to manually redirect here
    } catch (error: any) {
      console.error('Google sign-in error:', error)
      setError('Failed to sign in with Google. Please try again.')
      toast.error('Failed to sign in with Google. Please try again.')
    } finally {
      setIsLoading(false)
      setLoadingProvider(null)
    }
  }

  const handleFacebookSignIn = async () => {
    setIsLoading(true)
    setLoadingProvider('facebook')
    setError(null)

    try {
      await signInWithFacebook(selectedRole)
      // Note: Supabase OAuth will redirect automatically, so we don't need to manually redirect here
    } catch (error: any) {
      console.error('Facebook sign-in error:', error)
      setError('Failed to sign in with Facebook. Please try again.')
      toast.error('Failed to sign in with Facebook. Please try again.')
    } finally {
      setIsLoading(false)
      setLoadingProvider(null)
    }
  }

  // if (loading) {
  //   return (
  //     <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
  //       <motion.div
  //         initial={{ opacity: 0, scale: 0.8 }}
  //         animate={{ opacity: 1, scale: 1 }}
  //         className="flex items-center gap-3 text-white"
  //       >
  //         <Loader2 className="w-8 h-8 animate-spin text-cyan-400" />
  //         <span className="text-xl font-space-grotesk">Loading...</span>
  //       </motion.div>
  //     </div>
  //   )
  // }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
      {/* Quantum Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-500/5 rounded-full blur-2xl animate-pulse delay-500" />
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-cyan-400/30 rounded-full"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            animate={{
              y: [null, -100],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          {/* Back to Landing */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-6"
          >
            <Link href="/">
              <Button
                variant="ghost"
                className="text-gray-400 hover:text-white transition-colors group"
              >
                <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Landing
              </Button>
            </Link>
          </motion.div>

          <Card className="bg-black/40 backdrop-blur-xl border-gray-800/50 shadow-2xl">
            <CardHeader className="text-center space-y-4">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                className={`mx-auto w-16 h-16 bg-gradient-to-r ${roles.find(r => r.id === selectedRole)?.color || 'from-cyan-500 to-purple-500'} rounded-full flex items-center justify-center`}
              >
                {React.createElement(roles.find(r => r.id === selectedRole)?.icon || Rocket, { className: "w-8 h-8 text-white" })}
              </motion.div>

              <CardTitle className="text-2xl font-bold text-white font-space-grotesk">
                {selectedRole === 'parent' ? 'Parent Access Portal' :
                 selectedRole === 'admin' ? 'Admin Access Portal' :
                 'Welcome to NanoHero'}
              </CardTitle>

              <p className="text-gray-400 font-inter">
                {selectedRole === 'parent' ? 'Monitor your child\'s learning journey and safety' :
                 selectedRole === 'admin' ? 'Administrative access to platform management' :
                 'Sign in to start your quantum learning adventure'}
              </p>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Error Alert */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-4"
                >
                  <Alert variant="destructive" className="bg-red-900/20 border-red-500/50 text-red-200">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                </motion.div>
              )}

              {/* Role Selection */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-4"
              >
                <div className="text-center">
                  <h3 className="text-white font-semibold mb-2">I am a...</h3>
                  <p className="text-gray-400 text-sm">Choose your role to access the right dashboard</p>
                </div>

                <div className="grid grid-cols-1 gap-3">
                  {roles.map((role) => (
                    <motion.button
                      key={role.id}
                      onClick={() => setSelectedRole(role.id)}
                      className={`p-4 rounded-lg border transition-all duration-300 text-left ${
                        selectedRole === role.id
                          ? `bg-gradient-to-r ${role.color}/20 ${role.borderColor} border-2`
                          : 'bg-gray-800/30 border-gray-700/50 hover:border-gray-600/50'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${role.color} ${
                          selectedRole === role.id ? 'shadow-lg' : 'opacity-70'
                        }`}>
                          <role.icon className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-white font-medium">{role.name}</h4>
                          <p className="text-gray-400 text-sm">{role.description}</p>
                        </div>
                        {selectedRole === role.id && (
                          <div className="w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                      </div>
                    </motion.button>
                  ))}
                </div>
              </motion.div>

              <Separator className="bg-gray-800" />

              {/* Google Sign In */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                  className="w-full h-12 bg-white hover:bg-gray-100 text-gray-900 font-semibold transition-all duration-300 group relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity" />
                  {loadingProvider === 'google' ? (
                    <Loader2 className="w-5 h-5 animate-spin mr-3" />
                  ) : (
                    <Chrome className="w-5 h-5 mr-3" />
                  )}
                  Continue with Google
                </Button>
              </motion.div>

              {/* Separator */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="relative"
              >
                <Separator className="bg-gray-700" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="bg-black/40 px-3 text-gray-400 text-sm">or</span>
                </div>
              </motion.div>

              {/* Facebook Sign In */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Button
                  onClick={handleFacebookSignIn}
                  disabled={isLoading}
                  className="w-full h-12 bg-[#1877F2] hover:bg-[#166FE5] text-white font-semibold transition-all duration-300 group relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity" />
                  {loadingProvider === 'facebook' ? (
                    <Loader2 className="w-5 h-5 animate-spin mr-3" />
                  ) : (
                    <Facebook className="w-5 h-5 mr-3" />
                  )}
                  Continue with Facebook
                </Button>
              </motion.div>

              {/* Features Preview */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="mt-8 space-y-3"
              >
                <div className="flex items-center gap-3 text-gray-300">
                  <Sparkles className="w-4 h-4 text-cyan-400" />
                  <span className="text-sm">Gamified learning experience</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Zap className="w-4 h-4 text-purple-400" />
                  <span className="text-sm">AI-powered personalized mentoring</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Users className="w-4 h-4 text-blue-400" />
                  <span className="text-sm">Collaborative learning community</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Shield className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Safe and secure environment</span>
                </div>
              </motion.div>

              {/* Terms and Privacy */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="text-center text-xs text-gray-500 mt-6"
              >
                By signing in, you agree to our{' '}
                <Link href="/terms" className="text-cyan-400 hover:underline">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-cyan-400 hover:underline">
                  Privacy Policy
                </Link>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
