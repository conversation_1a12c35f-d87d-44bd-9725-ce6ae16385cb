import type { Metada<PERSON> } from 'next'
import { AuthProvider } from '@/contexts/AuthContext'
import { Toaster } from 'sonner'

export const metadata: Metadata = {
  title: 'Student Dashboard - NanoHero',
  description: 'Your quantum learning adventure starts here',
}

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      {children}
      <Toaster 
        position="top-center"
        toastOptions={{
          style: {
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            border: '1px solid rgba(34, 211, 238, 0.3)',
          },
        }}
      />
    </AuthProvider>
  )
}
